import { CacheService } from './cache';
import { createLogger } from '../utils/logger';
import { 
  UserIdentity, 
  UserStockData, 
  DeviceInfo, 
  StockInfo, 
  SyncRequest, 
  SyncResponse,
  UserDataBackup,
  Env 
} from '../types/api';

/**
 * 用户数据管理服务
 */
export class UserService {
  private cache: CacheService;
  private logger: ReturnType<typeof createLogger>;

  constructor(env: Env) {
    this.cache = new CacheService(env.STOCK_CONFIG, 'UserService');
    this.logger = createLogger('UserService');
  }

  /**
   * 生成用户数据缓存键
   * @param userId 用户ID
   * @returns 缓存键
   */
  private getUserDataKey(userId: string): string {
    return `user_stocks:${userId}`;
  }

  /**
   * 生成设备指纹
   * @param userAgent 用户代理字符串
   * @param additionalInfo 额外信息
   * @returns 设备指纹
   */
  generateDeviceId(userAgent: string, additionalInfo?: Record<string, any>): string {
    const data = JSON.stringify({
      userAgent,
      timestamp: Date.now(),
      ...additionalInfo
    });
    
    // 简单的哈希函数生成设备ID
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    return `device_${Math.abs(hash).toString(36)}_${Date.now().toString(36)}`;
  }

  /**
   * 创建用户标识
   * @param deviceId 设备ID
   * @param userId 用户自定义ID
   * @returns 用户标识
   */
  createUserIdentity(deviceId: string, userId?: string): UserIdentity {
    return {
      deviceId,
      userId: userId || deviceId,
      sessionId: `session_${Date.now().toString(36)}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  /**
   * 获取用户股票数据
   * @param userId 用户ID
   * @returns 用户股票数据
   */
  async getUserStockData(userId: string): Promise<UserStockData | null> {
    try {
      const key = this.getUserDataKey(userId);
      const data = await this.cache.get<UserStockData>(key);
      
      if (data) {
        this.logger.debug('User stock data retrieved', { userId, stockCount: data.stocks.length });
      }
      
      return data;
    } catch (error) {
      this.logger.error('Failed to get user stock data', { userId, error: (error as Error).message });
      return null;
    }
  }

  /**
   * 保存用户股票数据
   * @param userData 用户股票数据
   * @param ttl 缓存时间（秒）
   * @returns 是否成功
   */
  async saveUserStockData(userData: UserStockData, ttl: number = 86400 * 30): Promise<boolean> {
    try {
      const key = this.getUserDataKey(userData.userId);
      await this.cache.set(key, userData, ttl);
      
      this.logger.info('User stock data saved', { 
        userId: userData.userId, 
        stockCount: userData.stocks.length,
        version: userData.version
      });
      
      return true;
    } catch (error) {
      this.logger.error('Failed to save user stock data', { 
        userId: userData.userId, 
        error: (error as Error).message 
      });
      return false;
    }
  }

  /**
   * 同步用户股票数据
   * @param syncRequest 同步请求
   * @returns 同步响应
   */
  async syncUserStockData(syncRequest: SyncRequest): Promise<SyncResponse> {
    const { userIdentity, localStocks, localLastModified, forceOverwrite } = syncRequest;
    const userId = userIdentity.userId || userIdentity.deviceId;

    try {
      // 获取云端数据
      const cloudData = await this.getUserStockData(userId);
      
      // 如果云端没有数据，直接上传本地数据
      if (!cloudData) {
        const newUserData: UserStockData = {
          userId,
          stocks: localStocks,
          lastModified: new Date().toISOString(),
          deviceInfo: {
            deviceId: userIdentity.deviceId,
            lastSyncTime: new Date().toISOString(),
            userAgent: '', // 将在调用时填充
          },
          version: 1
        };

        await this.saveUserStockData(newUserData);
        
        return {
          success: true,
          data: {
            stocks: localStocks,
            lastModified: newUserData.lastModified,
            syncAction: 'local_to_cloud'
          }
        };
      }

      // 如果强制覆盖，直接使用本地数据
      if (forceOverwrite) {
        const updatedData: UserStockData = {
          ...cloudData,
          stocks: localStocks,
          lastModified: new Date().toISOString(),
          version: cloudData.version + 1,
          deviceInfo: {
            ...cloudData.deviceInfo,
            lastSyncTime: new Date().toISOString()
          }
        };

        await this.saveUserStockData(updatedData);
        
        return {
          success: true,
          data: {
            stocks: localStocks,
            lastModified: updatedData.lastModified,
            syncAction: 'local_to_cloud',
            conflictResolution: 'force_overwrite'
          }
        };
      }

      // 比较时间戳决定同步方向
      const cloudTime = new Date(cloudData.lastModified).getTime();
      const localTime = new Date(localLastModified).getTime();

      if (cloudTime > localTime) {
        // 云端数据更新，下载到本地
        return {
          success: true,
          data: {
            stocks: cloudData.stocks,
            lastModified: cloudData.lastModified,
            syncAction: 'cloud_to_local'
          }
        };
      } else if (localTime > cloudTime) {
        // 本地数据更新，上传到云端
        const updatedData: UserStockData = {
          ...cloudData,
          stocks: localStocks,
          lastModified: new Date().toISOString(),
          version: cloudData.version + 1,
          deviceInfo: {
            ...cloudData.deviceInfo,
            lastSyncTime: new Date().toISOString()
          }
        };

        await this.saveUserStockData(updatedData);
        
        return {
          success: true,
          data: {
            stocks: localStocks,
            lastModified: updatedData.lastModified,
            syncAction: 'local_to_cloud'
          }
        };
      } else {
        // 数据相同，无需同步
        return {
          success: true,
          data: {
            stocks: cloudData.stocks,
            lastModified: cloudData.lastModified,
            syncAction: 'no_change'
          }
        };
      }

    } catch (error) {
      this.logger.error('Sync failed', { userId, error: (error as Error).message });
      
      return {
        success: false,
        data: {
          stocks: localStocks,
          lastModified: localLastModified,
          syncAction: 'no_change'
        },
        message: '同步失败，请稍后重试'
      };
    }
  }

  /**
   * 删除用户数据
   * @param userId 用户ID
   * @returns 是否成功
   */
  async deleteUserData(userId: string): Promise<boolean> {
    try {
      const key = this.getUserDataKey(userId);
      await this.cache.delete(key);
      
      this.logger.info('User data deleted', { userId });
      return true;
    } catch (error) {
      this.logger.error('Failed to delete user data', { userId, error: (error as Error).message });
      return false;
    }
  }

  /**
   * 创建数据备份
   * @param userId 用户ID
   * @returns 备份数据
   */
  async createBackup(userId: string): Promise<UserDataBackup | null> {
    try {
      const userData = await this.getUserStockData(userId);
      if (!userData) {
        return null;
      }

      const backup: UserDataBackup = {
        exportTime: new Date().toISOString(),
        version: '1.0',
        userData,
        checksum: this.generateChecksum(userData)
      };

      return backup;
    } catch (error) {
      this.logger.error('Failed to create backup', { userId, error: (error as Error).message });
      return null;
    }
  }

  /**
   * 从备份恢复数据
   * @param backup 备份数据
   * @returns 是否成功
   */
  async restoreFromBackup(backup: UserDataBackup): Promise<boolean> {
    try {
      // 验证校验和
      const expectedChecksum = this.generateChecksum(backup.userData);
      if (backup.checksum !== expectedChecksum) {
        this.logger.error('Backup checksum mismatch', { 
          expected: expectedChecksum, 
          actual: backup.checksum 
        });
        return false;
      }

      // 恢复数据
      const success = await this.saveUserStockData(backup.userData);
      
      if (success) {
        this.logger.info('Data restored from backup', { 
          userId: backup.userData.userId,
          exportTime: backup.exportTime
        });
      }

      return success;
    } catch (error) {
      this.logger.error('Failed to restore from backup', { error: (error as Error).message });
      return false;
    }
  }

  /**
   * 生成数据校验和
   * @param data 数据
   * @returns 校验和
   */
  private generateChecksum(data: UserStockData): string {
    const content = JSON.stringify({
      userId: data.userId,
      stocks: data.stocks,
      lastModified: data.lastModified,
      version: data.version
    });

    // 简单的校验和算法
    let checksum = 0;
    for (let i = 0; i < content.length; i++) {
      checksum = ((checksum << 5) - checksum + content.charCodeAt(i)) & 0xffffffff;
    }
    
    return checksum.toString(16);
  }
}
