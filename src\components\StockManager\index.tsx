import { useState } from 'react';
import { Activity, Folder } from 'lucide-react';
import { useStockList } from '@/hooks/useStockList';
import { StockInput } from './StockInput';
import { StockList } from './StockList';
import { GroupManager } from './GroupManager';

interface StockManagerProps {
  onSelectStock?: (code: string) => void;
  selectedStock?: string | null;
  isFullScreen?: boolean;
}

export function StockManager({ onSelectStock, selectedStock, isFullScreen = false }: StockManagerProps) {
  const {
    stocks,
    addStock,
    removeStock,
    isLoading,
    error,
  } = useStockList();
  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);
  const [showRealTimeData, setShowRealTimeData] = useState(true);
  const [activeTab, setActiveTab] = useState<'stocks' | 'groups'>('stocks');

  // 显示通知
  const showNotification = (type: 'success' | 'error', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 3000);
  };

  // 处理添加股票
  const handleAddStock = async (code: string, name?: string) => {
    const result = await addStock(code, name);

    if (result.success) {
      showNotification('success', result.message || '股票添加成功');
    } else {
      showNotification('error', result.message || '添加失败');
    }

    return result;
  };

  // 处理删除股票
  const handleRemoveStock = (code: string) => {
    removeStock(code);
    showNotification('success', '股票删除成功');
    
    // 如果删除的是当前选中的股票，清除选中状态
    if (selectedStock === code && onSelectStock) {
      onSelectStock('');
    }
  };

  return (
    <div className={`${isFullScreen ? 'w-full h-full bg-white overflow-hidden flex flex-col' : 'card p-6 h-full'}`}>
      {/* 标签页导航 */}
      <div className={`border-b border-gray-200 ${isFullScreen ? 'mx-6 mb-6' : 'mb-6'}`}>
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('stocks')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'stocks'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center gap-2">
              <Activity className="w-4 h-4" />
              股票管理
            </div>
          </button>
          <button
            onClick={() => setActiveTab('groups')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'groups'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center gap-2">
              <Folder className="w-4 h-4" />
              分组管理
            </div>
          </button>
        </nav>
      </div>

      {/* 标签页内容 */}
      <div className={`flex-1 ${isFullScreen ? 'mx-6 overflow-hidden' : ''}`}>
        {activeTab === 'stocks' ? (
          <>
            {/* 股票输入 */}
            <div className="mb-6">
              <StockInput
                onAddStock={handleAddStock}
                isLoading={isLoading}
                isFullScreen={isFullScreen}
              />
            </div>

            {/* 股票列表 */}
            <div className="h-full">
              <StockList
                stocks={stocks}
                onRemoveStock={handleRemoveStock}
                onSelectStock={onSelectStock}
                selectedStock={selectedStock}
                showRealTimeData={showRealTimeData}
                isFullScreen={isFullScreen}
                enableGrouping={true}
              />
            </div>

            {/* 股票管理统计信息 */}
            {stocks.length > 0 && (
              <div className="pt-4 border-t border-gray-200 mt-6">
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <span>已添加 {stocks.length} 只股票</span>
                    {showRealTimeData && (
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">
                        实时监控
                      </span>
                    )}
                  </div>
                  <span>
                    {selectedStock ? `当前选中: ${selectedStock}` : '请选择股票查看数据'}
                  </span>
                </div>
              </div>
            )}
          </>
        ) : (
          /* 分组管理 */
          <div className="h-full">
            <GroupManager
              isFullScreen={isFullScreen}
              onGroupSelect={(groupId) => {
                // 可以在这里处理分组选择逻辑
                console.log('选中分组:', groupId);
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default StockManager;
