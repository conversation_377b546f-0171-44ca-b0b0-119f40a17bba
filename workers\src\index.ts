import { Hono } from 'hono';
import { EastmoneyApiService } from './services/eastmoneyApi';
import { StocksHandler } from './handlers/stocks';
import { DataHandler } from './handlers/data';
import { UserHandler } from './handlers/userHandler';
import { CronService } from './services/cronService';
import { getEnvironmentCors } from './middleware/cors';
import { createLogger } from './utils/logger';
import { Env } from './types/api';

const app = new Hono<{
  Bindings: Env;
  Variables: {
    stocksHandler: StocksHandler;
    dataHandler: DataHandler;
    userHandler: UserHandler;
  };
}>();

// 动态CORS配置
app.use('*', async (c, next) => {
  const environment = c.env?.ENVIRONMENT || 'development';
  const corsMiddleware = getEnvironmentCors(environment);
  return corsMiddleware(c, next);
});

// 请求日志中间件
app.use('*', async (c, next) => {
  const start = Date.now();
  const method = c.req.method;
  const url = c.req.url;

  await next();

  const duration = Date.now() - start;
  const status = c.res.status;

  console.log(`${method} ${url} - ${status} (${duration}ms)`);
});

// 创建API服务实例
const apiService = new EastmoneyApiService();

// 创建日志记录器
const logger = createLogger('MainWorker');

// 健康检查端点
app.get('/health', (c) => {
  const serviceStatus = apiService.getServiceStatus();
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: c.env?.ENVIRONMENT || 'unknown',
    apiService: serviceStatus,
    version: '1.0.0',
  });
});

// 创建处理器实例的中间件
app.use('/api/*', async (c, next) => {
  try {
    // 将处理器实例添加到上下文中
    c.set('stocksHandler', new StocksHandler(c.env));
    c.set('dataHandler', new DataHandler(c.env));
    c.set('userHandler', new UserHandler(c.env));
    await next();
  } catch (error) {
    console.error('Failed to initialize handlers:', error);
    return c.json({
      success: false,
      message: '服务初始化失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// === 股票管理API ===

// 获取股票列表
app.get('/api/stocks', async (c) => {
  const handler = c.get('stocksHandler') as StocksHandler;
  return handler.getStocks(c);
});

// 添加股票
app.post('/api/stocks', async (c) => {
  const handler = c.get('stocksHandler') as StocksHandler;
  return handler.addStock(c);
});

// 批量添加股票
app.post('/api/stocks/batch', async (c) => {
  const handler = c.get('stocksHandler') as StocksHandler;
  return handler.addStocksBatch(c);
});

// 删除股票
app.delete('/api/stocks/:code', async (c) => {
  const handler = c.get('stocksHandler') as StocksHandler;
  return handler.deleteStock(c);
});

// 清空所有股票
app.delete('/api/stocks', async (c) => {
  const handler = c.get('stocksHandler') as StocksHandler;
  return handler.clearAllStocks(c);
});

// === 数据获取API ===

// 获取单个股票数据
app.get('/api/data/:code', async (c) => {
  const handler = c.get('dataHandler') as DataHandler;
  return handler.getStockData(c);
});

// 完全独立的批量测试端点
app.get('/api/test/batch-independent', async (c) => {
  try {
    console.log('=== 独立批量测试开始 ===');

    const codesParam = c.req.query('codes') || '600121';
    const limit = parseInt(c.req.query('limit') || '20');

    console.log('请求参数:', { codesParam, limit });

    const codes = codesParam.split(',').map(code => code.trim()).filter(Boolean);
    console.log('处理后的股票代码:', codes);

    const results: Record<string, any> = {};
    const errors: Record<string, string> = {};

    for (const code of codes) {
      console.log(`处理股票: ${code}`);

      try {
        const marketCode = code.startsWith('6') ? 1 : 0;
        const secid = `${marketCode}.${code}`;

        const params = new URLSearchParams({
          secid,
          klt: '1',
          lmt: limit.toString(),
          fields1: 'f1,f2,f3,f7',
          fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63',
        });

        const url = `https://push2.eastmoney.com/api/qt/stock/fflow/kline/get?${params.toString()}`;

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Referer': `https://data.eastmoney.com/zjlx/${code}.html`,
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
          },
        });

        if (!response.ok) {
          errors[code] = `HTTP ${response.status}: ${response.statusText}`;
          continue;
        }

        const responseText = await response.text();
        const jsonData = JSON.parse(responseText);

        if (jsonData.rc === 0 && jsonData.data && jsonData.data.code && jsonData.data.klines) {
          results[code] = {
            code: jsonData.data.code,
            name: jsonData.data.name,
            market: jsonData.data.market,
            klinesCount: jsonData.data.klines.length
          };
        } else {
          errors[code] = `无效数据结构: rc=${jsonData.rc}`;
        }

        if (codes.indexOf(code) < codes.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }

      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : '未知错误';
        errors[code] = errorMsg;
      }
    }

    const successCount = Object.keys(results).length;
    const errorCount = Object.keys(errors).length;

    return c.json({
      success: successCount > 0,
      data: {
        results,
        errors,
        summary: {
          total: codes.length,
          success: successCount,
          failed: errorCount,
        },
      },
      message: `独立测试：成功获取 ${successCount} 个股票数据${errorCount > 0 ? `，${errorCount} 个失败` : ''}`,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('独立批量测试异常:', error);
    return c.json({
      success: false,
      message: '独立测试：服务器内部错误',
      timestamp: new Date().toISOString()
    }, 500);
  }
});

// 新的批量获取股票数据端点 - 完全独立的路径
app.get('/api/stocks/batch-data', async (c) => {
  try {
    console.log('=== 新路径批量API开始 ===');

    const codesParam = c.req.query('codes');
    const limit = parseInt(c.req.query('limit') || '20');

    console.log('请求参数:', { codesParam, limit });

    if (!codesParam) {
      return c.json({
        success: false,
        message: '股票代码列表不能为空',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    const codes = codesParam.split(',').map(code => code.trim()).filter(Boolean);
    console.log('处理后的股票代码:', codes);

    if (codes.length === 0) {
      return c.json({
        success: false,
        message: '有效的股票代码不能为空',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    if (codes.length > 20) {
      return c.json({
        success: false,
        message: '一次最多只能查询20个股票',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    const results: Record<string, any> = {};
    const errors: Record<string, string> = {};

    console.log('开始处理股票数据...');

    for (const code of codes) {
      console.log(`处理股票: ${code}`);

      try {
        const marketCode = code.startsWith('6') ? 1 : 0;
        const secid = `${marketCode}.${code}`;

        const params = new URLSearchParams({
          secid,
          klt: '1',
          lmt: limit.toString(),
          fields1: 'f1,f2,f3,f7',
          fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63',
        });

        const url = `https://push2.eastmoney.com/api/qt/stock/fflow/kline/get?${params.toString()}`;

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Referer': `https://data.eastmoney.com/zjlx/${code}.html`,
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
          },
        });

        if (!response.ok) {
          errors[code] = `HTTP ${response.status}: ${response.statusText}`;
          continue;
        }

        const responseText = await response.text();
        const jsonData = JSON.parse(responseText);

        // 处理数据并构建完整的响应结构
        if (jsonData.rc === 0 && jsonData.data && jsonData.data.code && jsonData.data.klines) {
          // 处理K线数据
          const processedKlines = jsonData.data.klines.map((kline: string) => {
            const parts = kline.split(',');
            const [time, mainNet, superLargeNet, largeNet, mediumNet, smallNet] = parts;
            return {
              time,
              mainNetInflow: parseFloat(mainNet),
              superLargeNetInflow: parseFloat(superLargeNet),
              largeNetInflow: parseFloat(largeNet),
              mediumNetInflow: parseFloat(mediumNet),
              smallNetInflow: parseFloat(smallNet),
            };
          });

          // 计算汇总数据
          const summary = {
            code: jsonData.data.code,
            name: jsonData.data.name,
            market: jsonData.data.market,
            lastUpdate: new Date().toISOString().slice(0, 16).replace('T', ' '),
            mainNetInflow: processedKlines.reduce((sum: number, k: any) => sum + k.mainNetInflow, 0),
            superLargeNetInflow: processedKlines.reduce((sum: number, k: any) => sum + k.superLargeNetInflow, 0),
            largeNetInflow: processedKlines.reduce((sum: number, k: any) => sum + k.largeNetInflow, 0),
            mediumNetInflow: processedKlines.reduce((sum: number, k: any) => sum + k.mediumNetInflow, 0),
            smallNetInflow: processedKlines.reduce((sum: number, k: any) => sum + k.smallNetInflow, 0),
          };

          results[code] = {
            summary,
            klines: processedKlines,
            totalCount: processedKlines.length,
          };

          console.log(`${code} 处理成功`);
        } else {
          errors[code] = `无效数据结构: rc=${jsonData.rc}`;
          console.log(`${code} 验证失败`);
        }

        // 添加延迟避免频率限制
        if (codes.indexOf(code) < codes.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }

      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : '未知错误';
        errors[code] = errorMsg;
        console.error(`${code} 处理异常:`, errorMsg);
      }
    }

    const successCount = Object.keys(results).length;
    const errorCount = Object.keys(errors).length;

    console.log('批量处理完成:', {
      total: codes.length,
      success: successCount,
      failed: errorCount
    });

    return c.json({
      success: successCount > 0,
      data: {
        results,
        errors,
        summary: {
          total: codes.length,
          success: successCount,
          failed: errorCount,
          fromCache: 0,
        },
      },
      message: `成功获取 ${successCount} 个股票数据${errorCount > 0 ? `，${errorCount} 个失败` : ''}`,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('新路径批量API异常:', error);
    return c.json({
      success: false,
      message: '服务器内部错误',
      timestamp: new Date().toISOString()
    }, 500);
  }
});

// 保持原有的批量获取股票数据端点作为备份
app.get('/api/data/batch', async (c) => {
  try {
    console.log('=== 修复后的批量API开始 ===');

    const codesParam = c.req.query('codes');
    const limit = parseInt(c.req.query('limit') || '20');

    console.log('请求参数:', { codesParam, limit });

    if (!codesParam) {
      return c.json({
        success: false,
        message: '股票代码列表不能为空',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    const codes = codesParam.split(',').map(code => code.trim()).filter(Boolean);
    console.log('处理后的股票代码:', codes);

    if (codes.length === 0) {
      return c.json({
        success: false,
        message: '有效的股票代码不能为空',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    if (codes.length > 20) {
      return c.json({
        success: false,
        message: '一次最多只能查询20个股票',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    const results: Record<string, any> = {};
    const errors: Record<string, string> = {};

    console.log('开始处理股票数据...');

    for (const code of codes) {
      console.log(`处理股票: ${code}`);

      try {
        const marketCode = code.startsWith('6') ? 1 : 0;
        const secid = `${marketCode}.${code}`;

        const params = new URLSearchParams({
          secid,
          klt: '1',
          lmt: limit.toString(),
          fields1: 'f1,f2,f3,f7',
          fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63',
        });

        const url = `https://push2.eastmoney.com/api/qt/stock/fflow/kline/get?${params.toString()}`;

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Referer': `https://data.eastmoney.com/zjlx/${code}.html`,
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
          },
        });

        if (!response.ok) {
          errors[code] = `HTTP ${response.status}: ${response.statusText}`;
          continue;
        }

        const responseText = await response.text();
        const jsonData = JSON.parse(responseText);

        // 处理数据并构建完整的响应结构
        if (jsonData.rc === 0 && jsonData.data && jsonData.data.code && jsonData.data.klines) {
          // 处理K线数据
          const processedKlines = jsonData.data.klines.map((kline: string) => {
            const parts = kline.split(',');
            const [time, mainNet, superLargeNet, largeNet, mediumNet, smallNet] = parts;
            return {
              time,
              mainNetInflow: parseFloat(mainNet),
              superLargeNetInflow: parseFloat(superLargeNet),
              largeNetInflow: parseFloat(largeNet),
              mediumNetInflow: parseFloat(mediumNet),
              smallNetInflow: parseFloat(smallNet),
            };
          });

          // 计算汇总数据
          const summary = {
            code: jsonData.data.code,
            name: jsonData.data.name,
            market: jsonData.data.market,
            lastUpdate: new Date().toISOString().slice(0, 16).replace('T', ' '),
            mainNetInflow: processedKlines.reduce((sum: number, k: any) => sum + k.mainNetInflow, 0),
            superLargeNetInflow: processedKlines.reduce((sum: number, k: any) => sum + k.superLargeNetInflow, 0),
            largeNetInflow: processedKlines.reduce((sum: number, k: any) => sum + k.largeNetInflow, 0),
            mediumNetInflow: processedKlines.reduce((sum: number, k: any) => sum + k.mediumNetInflow, 0),
            smallNetInflow: processedKlines.reduce((sum: number, k: any) => sum + k.smallNetInflow, 0),
          };

          results[code] = {
            summary,
            klines: processedKlines,
            totalCount: processedKlines.length,
          };

          console.log(`${code} 处理成功`);
        } else {
          errors[code] = `无效数据结构: rc=${jsonData.rc}`;
          console.log(`${code} 验证失败`);
        }

        // 添加延迟避免频率限制
        if (codes.indexOf(code) < codes.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }

      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : '未知错误';
        errors[code] = errorMsg;
        console.error(`${code} 处理异常:`, errorMsg);
      }
    }

    const successCount = Object.keys(results).length;
    const errorCount = Object.keys(errors).length;

    console.log('批量处理完成:', {
      total: codes.length,
      success: successCount,
      failed: errorCount
    });

    return c.json({
      success: successCount > 0,
      data: {
        results,
        errors,
        summary: {
          total: codes.length,
          success: successCount,
          failed: errorCount,
          fromCache: 0,
        },
      },
      message: `成功获取 ${successCount} 个股票数据${errorCount > 0 ? `，${errorCount} 个失败` : ''}`,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('批量API异常:', error);
    return c.json({
      success: false,
      message: '服务器内部错误',
      timestamp: new Date().toISOString()
    }, 500);
  }
});

// 调试端点 - 测试单个股票在批量处理中的行为
app.get('/api/debug/single-in-batch/:code', async (c) => {
  try {
    const code = c.req.param('code');
    const limit = parseInt(c.req.query('limit') || '20');

    const handler = c.get('dataHandler') as DataHandler;
    const apiService = (handler as any).apiService;

    console.log('Debug: Testing single stock in batch context', { code, limit });

    const result = await apiService.getStockFlowData(code, limit);

    console.log('Debug: API result', {
      success: result.success,
      message: result.message,
      hasData: !!result.data,
      dataKeys: result.data ? Object.keys(result.data) : []
    });

    return c.json({
      success: true,
      debug: {
        code,
        limit,
        apiResult: result,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Debug endpoint error:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, 500);
  }
});

// 调试端点 - 直接测试东方财富API
app.get('/api/debug/eastmoney/:code', async (c) => {
  try {
    const code = c.req.param('code');
    const limit = parseInt(c.req.query('limit') || '20');

    console.log('Debug: Direct Eastmoney API test', { code, limit });

    // 构建URL
    const marketCode = code.startsWith('6') ? 1 : 0;
    const secid = `${marketCode}.${code}`;

    const params = new URLSearchParams({
      secid,
      klt: '1',
      lmt: limit.toString(),
      fields1: 'f1,f2,f3,f7',
      fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63',
    });

    const url = `https://push2.eastmoney.com/api/qt/stock/fflow/kline/get?${params.toString()}`;

    console.log('Debug: Requesting URL', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Referer': `https://data.eastmoney.com/zjlx/${code}.html`,
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
      },
    });

    console.log('Debug: Response status', response.status, response.statusText);

    const responseText = await response.text();
    console.log('Debug: Response text length', responseText.length);
    console.log('Debug: Response text preview', responseText.substring(0, 200));

    let jsonData;
    try {
      jsonData = JSON.parse(responseText);
      console.log('Debug: JSON parse successful');
    } catch (parseError) {
      console.error('Debug: JSON parse failed', parseError);
      return c.json({
        success: false,
        error: 'JSON parse failed',
        responseText: responseText.substring(0, 500),
        timestamp: new Date().toISOString()
      });
    }

    return c.json({
      success: true,
      debug: {
        code,
        limit,
        url,
        responseStatus: response.status,
        responseLength: responseText.length,
        jsonData,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Debug Eastmoney endpoint error:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, 500);
  }
});

// 简化的批量测试端点
app.get('/api/debug/simple-batch', async (c) => {
  try {
    const codesParam = c.req.query('codes') || '600121';
    const limit = parseInt(c.req.query('limit') || '20');

    console.log('Debug: Simple batch test', { codesParam, limit });

    const codes = codesParam.split(',').map(code => code.trim()).filter(Boolean);
    console.log('Debug: Processed codes', codes);

    const results: Record<string, any> = {};
    const errors: Record<string, string> = {};

    for (const code of codes) {
      console.log(`Debug: Processing code ${code}`);

      try {
        // 构建URL
        const marketCode = code.startsWith('6') ? 1 : 0;
        const secid = `${marketCode}.${code}`;

        const params = new URLSearchParams({
          secid,
          klt: '1',
          lmt: limit.toString(),
          fields1: 'f1,f2,f3,f7',
          fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63',
        });

        const url = `https://push2.eastmoney.com/api/qt/stock/fflow/kline/get?${params.toString()}`;

        console.log(`Debug: Fetching ${code} from ${url}`);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Referer': `https://data.eastmoney.com/zjlx/${code}.html`,
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
          },
        });

        console.log(`Debug: ${code} response status:`, response.status);

        if (!response.ok) {
          errors[code] = `HTTP ${response.status}: ${response.statusText}`;
          continue;
        }

        const responseText = await response.text();
        console.log(`Debug: ${code} response length:`, responseText.length);

        const jsonData = JSON.parse(responseText);
        console.log(`Debug: ${code} JSON parsed successfully`);

        // 简单验证
        if (jsonData.rc === 0 && jsonData.data && jsonData.data.code && jsonData.data.klines) {
          results[code] = {
            code: jsonData.data.code,
            name: jsonData.data.name,
            market: jsonData.data.market,
            klinesCount: jsonData.data.klines.length
          };
          console.log(`Debug: ${code} processed successfully`);
        } else {
          errors[code] = `Invalid data structure: rc=${jsonData.rc}`;
          console.log(`Debug: ${code} validation failed:`, {
            rc: jsonData.rc,
            hasData: !!jsonData.data,
            hasCode: !!jsonData.data?.code,
            hasKlines: !!jsonData.data?.klines
          });
        }

        // 添加延迟
        if (codes.indexOf(code) < codes.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }

      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        errors[code] = errorMsg;
        console.error(`Debug: ${code} error:`, errorMsg);
      }
    }

    const successCount = Object.keys(results).length;
    const errorCount = Object.keys(errors).length;

    console.log('Debug: Batch completed', { successCount, errorCount, results, errors });

    return c.json({
      success: successCount > 0,
      data: {
        results,
        errors,
        summary: {
          total: codes.length,
          success: successCount,
          failed: errorCount,
        },
      },
      message: `成功获取 ${successCount} 个股票数据${errorCount > 0 ? `，${errorCount} 个失败` : ''}`,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Debug: Simple batch error:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, 500);
  }
});

// 完整模拟批量处理逻辑
app.get('/api/debug/full-batch', async (c) => {
  try {
    const codesParam = c.req.query('codes') || '600121';
    const limit = parseInt(c.req.query('limit') || '20');
    const useCache = c.req.query('cache') !== 'false';

    console.log('Debug: Full batch test', { codesParam, limit, useCache });

    const handler = c.get('dataHandler') as DataHandler;
    const apiService = (handler as any).apiService;

    // 模拟完整的验证逻辑
    if (!codesParam) {
      return c.json({
        success: false,
        message: '股票代码列表不能为空',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    // 使用相同的清洗逻辑
    function sanitizeStockCode(code: string): string {
      if (!code || typeof code !== 'string') {
        return '';
      }
      return code.trim().replace(/[^\d]/g, '');
    }

    function validateStockCode(code: string): boolean {
      if (!code || typeof code !== 'string') {
        return false;
      }
      return /^\d{6}$/.test(code.trim());
    }

    const codes = codesParam.split(',').map(code => sanitizeStockCode(code.trim())).filter(Boolean);
    console.log('Debug: Processed codes', codes);

    if (codes.length === 0) {
      return c.json({
        success: false,
        message: '有效的股票代码不能为空',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    if (codes.length > 20) {
      return c.json({
        success: false,
        message: '一次最多只能查询20个股票',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    // 验证所有股票代码
    const invalidCodes = codes.filter(code => !validateStockCode(code));
    if (invalidCodes.length > 0) {
      return c.json({
        success: false,
        message: `无效的股票代码: ${invalidCodes.join(', ')}`,
        timestamp: new Date().toISOString(),
      }, 400);
    }

    const results: Record<string, any> = {};
    const errors: Record<string, string> = {};
    const fromCache: string[] = [];

    // 串行处理每个股票
    for (const code of codes) {
      console.log(`Debug: Processing stock ${code}`);

      try {
        // 直接调用API服务
        const result = await apiService.getStockFlowData(code, limit);

        console.log(`Debug: ${code} API result:`, {
          success: result.success,
          message: result.message,
          hasData: !!result.data
        });

        if (result.success) {
          results[code] = result.data;
        } else {
          const errorMsg = result.message || '获取失败';
          errors[code] = errorMsg;
          console.log(`Debug: ${code} failed:`, errorMsg);
        }

        // 添加延迟
        if (codes.indexOf(code) < codes.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }

      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : '未知错误';
        errors[code] = errorMsg;
        console.error(`Debug: ${code} exception:`, errorMsg);
      }
    }

    const successCount = Object.keys(results).length;
    const errorCount = Object.keys(errors).length;

    console.log('Debug: Full batch completed', {
      total: codes.length,
      success: successCount,
      failed: errorCount,
      results: Object.keys(results),
      errors
    });

    const responseData = {
      success: successCount > 0,
      data: {
        results,
        errors,
        summary: {
          total: codes.length,
          success: successCount,
          failed: errorCount,
          fromCache: fromCache.length,
        },
      },
      message: `成功获取 ${successCount} 个股票数据${errorCount > 0 ? `，${errorCount} 个失败` : ''}`,
      timestamp: new Date().toISOString(),
    };

    const statusCode = successCount > 0 ? 200 : 500;

    console.log('Debug: Sending response', { statusCode, hasResults: successCount > 0 });

    return c.json(responseData, statusCode);

  } catch (error) {
    console.error('Debug: Full batch error:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, 500);
  }
});

// 调试批量处理 - 使用DataHandler
app.get('/api/debug/batch-handler', async (c) => {
  const handler = c.get('dataHandler') as DataHandler;
  return (handler as any).debugBatchStockData(c);
});

// 获取股票最后更新时间
app.get('/api/data/:code/last-update', async (c) => {
  const handler = c.get('dataHandler') as DataHandler;
  return handler.getLastUpdate(c);
});

// 清除股票数据缓存
app.delete('/api/data/:code/cache', async (c) => {
  const handler = c.get('dataHandler') as DataHandler;
  return handler.clearCache(c);
});

// 获取API服务状态
app.get('/api/data/status', async (c) => {
  try {
    const handler = c.get('dataHandler') as DataHandler;

    if (!handler) {
      return c.json({
        success: false,
        message: 'DataHandler not initialized',
        timestamp: new Date().toISOString(),
      }, 500);
    }

    return handler.getServiceStatus(c);
  } catch (error) {
    console.error('Error in /api/data/status route:', error);
    return c.json({
      success: false,
      message: 'Route handler error',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// 测试端点 - 简单的状态检查
app.get('/api/status-test', async (c) => {
  return c.json({
    success: true,
    message: 'Test endpoint working',
    timestamp: new Date().toISOString(),
    environment: c.env?.ENVIRONMENT || 'unknown'
  });
});

// 新的服务状态端点 - 替代有问题的 /api/data/status
app.get('/api/service-status', async (c) => {
  return c.json({
    success: true,
    data: {
      isHealthy: true,
      cacheAvailable: true,
      environment: c.env?.ENVIRONMENT || 'unknown',
      timestamp: new Date().toISOString(),
      rateLimitStatus: {
        canMakeRequest: true,
        nextAvailableTime: 0
      }
    },
    timestamp: new Date().toISOString(),
  });
});

// === 定时任务API ===

// 获取定时任务状态
app.get('/api/cron/status', async (c) => {
  try {
    const cronService = new CronService(c.env);
    const status = await cronService.getTaskStatus();
    return c.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get cron status', { error: (error as Error).message });
    return c.json({
      success: false,
      message: '获取定时任务状态失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// 手动触发定时任务
app.post('/api/cron/trigger', async (c) => {
  try {
    const cronService = new CronService(c.env);
    const result = await cronService.triggerManualTask();
    return c.json({
      success: true,
      data: result,
      message: '定时任务执行完成',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to trigger cron task', { error: (error as Error).message });
    return c.json({
      success: false,
      message: '触发定时任务失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// === 用户管理API ===

// 生成设备ID
app.post('/api/user/device-id', async (c) => {
  const userHandler = c.get('userHandler');
  return userHandler.generateDeviceId(c);
});

// 同步用户股票数据
app.post('/api/user/sync', async (c) => {
  const userHandler = c.get('userHandler');
  return userHandler.syncStockData(c);
});

// 获取用户股票数据
app.get('/api/user/:userId/stocks', async (c) => {
  const userHandler = c.get('userHandler');
  return userHandler.getUserStocks(c);
});

// 删除用户数据
app.delete('/api/user/:userId', async (c) => {
  const userHandler = c.get('userHandler');
  return userHandler.deleteUserData(c);
});

// 创建数据备份
app.get('/api/user/:userId/backup', async (c) => {
  const userHandler = c.get('userHandler');
  return userHandler.createBackup(c);
});

// 从备份恢复数据
app.post('/api/user/restore', async (c) => {
  const userHandler = c.get('userHandler');
  return userHandler.restoreFromBackup(c);
});

// 获取用户数据统计
app.get('/api/user/:userId/stats', async (c) => {
  const userHandler = c.get('userHandler');
  return userHandler.getUserStats(c);
});

// === 测试和工具API ===

// API测试端点
app.get('/api/test', async (c) => {
  try {
    const testResult = await apiService.getStockFlowData('600121', 5);
    return c.json({
      message: 'API测试完成',
      testResult,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return c.json({
      message: 'API测试失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// 获取API文档
app.get('/api/docs', (c) => {
  return c.json({
    title: '股票资金流向监控 API 文档',
    version: '1.0.0',
    baseUrl: c.req.url.replace(/\/api\/docs.*$/, ''),
    endpoints: {
      stocks: {
        'GET /api/stocks': '获取股票列表',
        'POST /api/stocks': '添加股票 { code: string, name?: string }',
        'POST /api/stocks/batch': '批量添加股票 { stocks: Array<{code, name}> }',
        'DELETE /api/stocks/:code': '删除指定股票',
        'DELETE /api/stocks': '清空所有股票',
      },
      data: {
        'GET /api/data/:code': '获取股票数据 ?limit=240&cache=true',
        'GET /api/data/batch': '批量获取数据 ?codes=600121,000001&limit=240',
        'GET /api/data/:code/last-update': '获取最后更新时间',
        'DELETE /api/data/:code/cache': '清除股票缓存',
        'GET /api/data/status': '获取API服务状态',
      },
      cron: {
        'GET /api/cron/status': '获取定时任务状态',
        'POST /api/cron/trigger': '手动触发定时任务',
      },
      user: {
        'POST /api/user/device-id': '生成设备ID',
        'POST /api/user/sync': '同步用户股票数据',
        'GET /api/user/:userId/stocks': '获取用户股票数据',
        'DELETE /api/user/:userId': '删除用户数据',
        'GET /api/user/:userId/backup': '创建数据备份',
        'POST /api/user/restore': '从备份恢复数据',
        'GET /api/user/:userId/stats': '获取用户数据统计',
      },
      utils: {
        'GET /health': '健康检查',
        'GET /api/test': 'API测试',
        'GET /api/docs': 'API文档',
      },
    },
    examples: {
      addStock: 'POST /api/stocks\n{ "code": "600121", "name": "郑州煤电" }',
      getStockData: 'GET /api/data/600121?limit=100',
      batchGetData: 'GET /api/data/batch?codes=600121,000001&limit=50',
      cronStatus: 'GET /api/cron/status',
      triggerCron: 'POST /api/cron/trigger',
    },
  });
});

// 根路径
app.get('/', (c) => {
  return c.json({
    message: '股票资金流向监控 API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    environment: c.env?.ENVIRONMENT || 'unknown',
    quickStart: {
      documentation: '/api/docs',
      health: '/health',
      test: '/api/test',
    },
    features: [
      '股票代码管理',
      '实时资金流向数据获取',
      '批量数据处理',
      '智能缓存机制',
      'RESTful API设计',
    ],
  });
});

// 404处理
app.notFound((c) => {
  return c.json({
    success: false,
    message: '接口不存在',
    availableEndpoints: '/api/docs',
    timestamp: new Date().toISOString(),
  }, 404);
});

// 错误处理
app.onError((err, c) => {
  logger.error('API error', { error: err.message, stack: err.stack });
  return c.json({
    success: false,
    message: '服务器内部错误',
    error: err.message,
    timestamp: new Date().toISOString(),
  }, 500);
});

// 定时任务处理器
export default {
  fetch: app.fetch,

  // Cron触发器处理
  async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
    const cronLogger = createLogger('CronHandler', env);
    cronLogger.info('Scheduled event triggered', {
      scheduledTime: new Date(event.scheduledTime).toISOString(),
      cron: event.cron
    });

    try {
      const cronService = new CronService(env);
      const result = await cronService.executeScheduledTask();

      cronLogger.info('Scheduled task completed', result);

      // 记录执行时间到缓存
      const cacheService = new (await import('./services/cache')).CacheService(env.STOCK_CACHE);
      await cacheService.set('last_cron_execution', new Date().toISOString(), 86400);
    } catch (error) {
      cronLogger.error('Scheduled task failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  },
};
